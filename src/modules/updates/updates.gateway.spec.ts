import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';

import { UpdatesRoomsService } from './updates-rooms.service';
import { UpdatesGateway } from './updates.gateway';
import { UpdatesEvents, UpdatesMessageBody } from './updates.types';
import { SessionsService } from '../sessions/sessions.service';
import { UsersService } from '../users/users.service';
import { WorkersService } from '../workers/workers.service';

describe('UpdatesGateway', () => {
  let gateway: UpdatesGateway;
  let mockUpdatesRoomsService: jest.Mocked<UpdatesRoomsService>;
  let mockWorkersService: jest.Mocked<WorkersService>;
  let mockUsersService: jest.Mocked<UsersService>;
  let mockJwtService: jest.Mocked<JwtService>;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockSessionsService: jest.Mocked<SessionsService>;

  beforeEach(async () => {
    const mockUpdatesRoomsServiceProvider = {
      provide: UpdatesRoomsService,
      useValue: {
        joinUserRooms: jest.fn(),
      },
    };

    const mockWorkersServiceProvider = {
      provide: WorkersService,
      useValue: {
        findOne: jest.fn(),
      },
    };

    const mockUsersServiceProvider = {
      provide: UsersService,
      useValue: {
        getUserRoleInfo: jest.fn(),
      },
    };

    const mockJwtServiceProvider = {
      provide: JwtService,
      useValue: {
        verifyAsync: jest.fn(),
      },
    };

    const mockConfigServiceProvider = {
      provide: ConfigService,
      useValue: {
        get: jest.fn(),
      },
    };

    const mockSessionsServiceProvider = {
      provide: SessionsService,
      useValue: {
        findActive: jest.fn(),
        updateLastUsed: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpdatesGateway,
        mockUpdatesRoomsServiceProvider,
        mockWorkersServiceProvider,
        mockUsersServiceProvider,
        mockJwtServiceProvider,
        mockConfigServiceProvider,
        mockSessionsServiceProvider,
      ],
    }).compile();

    gateway = module.get<UpdatesGateway>(UpdatesGateway);
    mockUpdatesRoomsService = module.get(UpdatesRoomsService);
    mockWorkersService = module.get(WorkersService);
    mockUsersService = module.get(UsersService);
    mockJwtService = module.get(JwtService);
    mockConfigService = module.get(ConfigService);
    mockSessionsService = module.get(SessionsService);

    // Mock the logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(gateway).toBeDefined();
  });

  describe('Event Handler Coverage', () => {
    it('should handle all defined events without throwing errors', async () => {
      // Mock server
      gateway.server = {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn(),
        in: jest.fn().mockReturnThis(),
        socketsJoin: jest.fn(),
        socketsLeave: jest.fn(),
      } as any;

      // Mock worker service response
      mockWorkersService.findOne.mockResolvedValue({
        id: 'worker-1',
        userId: 'user-1',
        projectId: 'project-1',
        partnerId: 'partner-1',
        employmentStatus: 'active',
      } as any);

      // Mock user service response
      mockUsersService.getUserRoleInfo.mockResolvedValue({
        type: 'worker',
        entityId: 'worker-1',
      } as any);

      // Test all event types
      const testEvents: Array<{ type: UpdatesEvents; payload: any }> = [
        {
          type: UpdatesEvents.DAILY_REPORT_CREATED,
          payload: { reportId: 'report-1' },
        },
        {
          type: UpdatesEvents.DAILY_REPORT_UPDATED,
          payload: { reportId: 'report-1', status: 'approved' },
        },
        {
          type: UpdatesEvents.DAILY_REPORT_FINISHED,
          payload: { reportId: 'report-1', workerId: 'worker-1' },
        },
        {
          type: UpdatesEvents.PRESENCE_VALIDATION_REQUESTED,
          payload: { validationId: 'validation-1' },
        },
        {
          type: UpdatesEvents.REGISTRATION_REQUEST_RECEIVED,
          payload: { requestId: 'request-1' },
        },
        {
          type: UpdatesEvents.WORKER_STATUS_CHANGED,
          payload: { workerId: 'worker-1', status: 'started' },
        },
        {
          type: UpdatesEvents.PROJECT_UPDATED,
          payload: { projectId: 'project-1', partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.PROJECT_CREATED,
          payload: { projectId: 'project-1', partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.PROJECT_DELETED,
          payload: { projectId: 'project-1', partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.USER_UPDATED,
          payload: { userId: 'user-1' },
        },
        {
          type: UpdatesEvents.WORKER_UPDATED,
          payload: { workerId: 'worker-1' },
        },
        {
          type: UpdatesEvents.PARTNER_UPDATED,
          payload: { partnerId: 'partner-1' },
        },
        {
          type: UpdatesEvents.WORKER_EMPLOYMENT_CHANGED,
          payload: { workerId: 'worker-1', status: 'active' },
        },
        {
          type: UpdatesEvents.WORKER_APPROVAL_CHANGED,
          payload: { workerId: 'worker-1', status: 'approved' },
        },
        {
          type: UpdatesEvents.WORKER_PRESENCE_CHANGED,
          payload: { workerId: 'worker-1', status: 'validated' },
        },
        {
          type: UpdatesEvents.MANAGER_UPDATED,
          payload: { managerId: 'manager-1' },
        },
        {
          type: UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT,
          payload: { managerId: 'manager-1', projectId: 'project-1' },
        },
        {
          type: UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT,
          payload: { managerId: 'manager-1', projectId: 'project-1' },
        },
        {
          type: UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED,
          payload: { managerId: 'manager-1', status: 'active' },
        },
        {
          type: UpdatesEvents.USER_DELETED,
          payload: { userId: 'user-1' },
        },
        {
          type: UpdatesEvents.PROJECT_MANAGER_UPDATED,
          payload: {
            projectId: 'project-1',
            managerId: 'manager-1',
            action: 'assigned',
          },
        },
      ];

      // Test each event
      for (const event of testEvents) {
        await expect(
          gateway.sendMessage(event as UpdatesMessageBody<any>),
        ).resolves.not.toThrow();
      }
    });

    it('should handle unknown event types gracefully', async () => {
      // Mock server
      gateway.server = {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn(),
      } as any;

      const unknownEvent = {
        type: 'unknown.event' as any,
        payload: { test: 'data' },
      };

      await expect(
        gateway.sendMessage(unknownEvent as UpdatesMessageBody<any>),
      ).resolves.not.toThrow();
    });
  });

  describe('Socket Room Management', () => {
    beforeEach(() => {
      gateway.server = {
        to: jest.fn().mockReturnThis(),
        emit: jest.fn(),
        in: jest.fn().mockReturnThis(),
        socketsJoin: jest.fn(),
        socketsLeave: jest.fn(),
      } as any;
    });

    it('should properly handle project deletion room management', async () => {
      const message = {
        type: UpdatesEvents.PROJECT_DELETED,
        payload: { projectId: 'project-1', partnerId: 'partner-1' },
      };

      await gateway.sendMessage(message);

      expect(gateway.server.socketsLeave).toHaveBeenCalledWith(
        'project:project-1',
      );
    });

    it('should properly handle manager removal from project', async () => {
      const message = {
        type: UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT,
        payload: { managerId: 'manager-1', projectId: 'project-1' },
      };

      await gateway.sendMessage(message);

      expect(gateway.server.in).toHaveBeenCalledWith('manager:manager-1');
      expect(gateway.server.socketsLeave).toHaveBeenCalledWith(
        'project:project-1',
      );
    });
  });
});
