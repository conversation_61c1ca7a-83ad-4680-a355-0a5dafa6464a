import {
  Controller,
  Get,
  HttpStatus,
  Inject,
  NotFoundException,
  Param,
  Patch,
  Query,
  UseGuards,
  forwardRef,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { Forwarded } from '@/common/types';

import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { DailyReportsService } from '../daily-reports/daily-reports.service';
import { WorkerFilterParamsDto } from './dto/worker-filter-params.dto';
import {
  WorkerWithUserDto,
  WorkersWithBasicUserAndReportDto,
} from './dto/worker.dto';
import { WorkersValidTimeDto } from './dto/workers-valid-time.dto';
import { WorkersService } from './workers.service';
import { DailyReportWithPhotosDto } from '../daily-reports/dto/daily-report.dto';
import { ReportFilterParamsDto } from '../daily-reports/dto/daily-reports-filter-params.dto';

@ApiTags('Workers')
@ApiBearerAuth()
@Controller('workers')
@UseGuards(JwtAuthGuard, RolesGuard)
export class WorkersController {
  constructor(
    private readonly workersService: WorkersService,
    @Inject(forwardRef(() => DailyReportsService))
    private readonly dailyReportsService: Forwarded<DailyReportsService>,
  ) {}

  @Get()
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'List all workers',
    description:
      'Retrieve a list of workers with optional filtering capabilities',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workers retrieved successfully',
    type: [WorkersWithBasicUserAndReportDto],
  })
  @ApiForbiddenResponse({ description: 'Not authorized to access workers' })
  findAll(
    @User() user: RequestUserType,
    @Query() filterParams?: WorkerFilterParamsDto,
  ): Promise<WorkersWithBasicUserAndReportDto[]> {
    return this.workersService.findAll(user, filterParams);
  }

  @Get('me')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Get current worker profile',
    description: 'Retrieve detailed information about the authenticated worker',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker profile retrieved successfully',
    type: WorkerWithUserDto,
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this profile',
  })
  async findMe(@User() user: RequestUserType): Promise<WorkerWithUserDto> {
    const worker = await this.workersService.findOneWithUser(
      user.entityId,
      user,
    );
    if (!worker) throw new NotFoundException('Worker not found');
    return worker;
  }

  @Get(':workerId')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker by ID',
    description: 'Retrieve detailed information about a specific worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker retrieved successfully',
    type: WorkerWithUserDto,
  })
  @ApiNotFoundResponse({ description: 'Worker not found' })
  findOne(
    @Param('workerId') workerId: string,
    @User() user: RequestUserType,
  ): Promise<WorkerWithUserDto> {
    return this.workersService.findOneWithUser(workerId, user);
  }

  @Get(':workerId/daily-reports')
  @Roles(Role.Worker, Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker daily reports',
    description: 'Retrieve all daily reports for a specific worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiQuery({ type: ReportFilterParamsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Daily reports retrieved successfully',
    type: [DailyReportWithPhotosDto],
  })
  findAllByWorkerId(
    @Param('workerId') workerId: string,
    @Query() filterParams: ReportFilterParamsDto,
  ): Promise<DailyReportWithPhotosDto[]> {
    return this.dailyReportsService.findAllByWorkerId(workerId, filterParams);
  }

  @Get(':workerId/daily-reports/approved-hours/:date')
  @Roles(Role.Worker, Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker approved hours',
    description:
      'Calculate total approved hours for a worker on a specific date',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiParam({
    name: 'date',
    description: 'Date in YYYY-MM-DD format',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Approved hours calculated successfully',
    type: Number,
  })
  findAllApprovedHoursByWorkerId(
    @Param('workerId') workerId: string,
    @Param('date') date: string,
  ): Promise<number> {
    return this.dailyReportsService.calculateApprovedHoursForWorker(
      workerId,
      date,
    );
  }

  @Patch('me/quit')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Quit job',
    description: 'Worker voluntarily ends their employment',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employment ended successfully',
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to perform this action',
  })
  quitJob(@User() user: RequestUserType) {
    return this.workersService.endEmployment(user.entityId, 'quit', user.id);
  }

  @Patch('me/employer/:registrationCode')
  @Roles(Role.Worker)
  @ApiOperation({
    summary: 'Change employer',
    description:
      'Worker changes their employer using a registration code (tip: use the "me/metadata" endpoint to update worker\'s employment information)',
  })
  @ApiParam({
    name: 'registrationCode',
    description: 'New employer registration code',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employer changed successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid registration code' })
  @ApiForbiddenResponse({ description: 'Not authorized to change employer' })
  changeEmployer(
    @Param('registrationCode') registrationCode: string,
    @User() user: RequestUserType,
  ) {
    return this.workersService.changeEmployer(
      user.id,
      registrationCode,
      !user.entityId ? undefined : user.entityId,
    );
  }

  @Get('me/valid-time')
  @Roles({
    type: Role.Worker,
    settings: {
      mustBeEmployed: true,
      mustBeApproved: true,
    },
  })
  @ApiOperation({
    summary: 'Get valid working time',
    description:
      'Returns the valid working time in seconds for the current day, accounting for pauses',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Valid working time retrieved successfully',
    type: WorkersValidTimeDto,
  })
  async getValidWorkingTime(
    @User() user: RequestUserType,
  ): Promise<WorkersValidTimeDto> {
    return this.dailyReportsService.calculateValidWorkingTime(user.entityId);
  }
}
