# Real-Time Updates (WebSocket API)

This document describes the comprehensive WebSocket API for receiving real-time updates from the server. All 23 events are fully implemented with proper room targeting and complete payload data.

## 📋 Table of Contents

- [Connection Details](#connection-details)
- [Authentication](#authentication)
- [Room Structure](#room-structure)
- [Events Overview](#events-overview)
- [Daily Report Events](#daily-report-events)
- [Worker Events](#worker-events)
- [Project Events](#project-events)
- [User Events](#user-events)
- [Partner Events](#partner-events)
- [Manager Events](#manager-events)
- [Validation Events](#validation-events)
- [Registration Events](#registration-events)
- [Error Handling](#error-handling)
- [Example Usage](#example-usage)
- [Best Practices](#best-practices)

## Connection Details

**Namespace:** `/updates`
**Supported Transports:** WebSocket, Polling
**CORS:** Enabled (all origins)

### Heartbeat Configuration

The server uses a heartbeat mechanism to maintain connection health:

- `pingInterval`: 25000ms (25 seconds) - How often the server sends ping packets
- `pingTimeout`: 10000ms (10 seconds) - How long to wait for a pong response

If a client fails to respond to a ping within the timeout period, the connection will be automatically terminated.

## Authentication

Authentication is required to establish a WebSocket connection. Provide a JWT token in the connection handshake:

```javascript
const socket = io('/updates', {
  auth: {
    token: 'your-jwt-token',
  },
  // Optional: Override default heartbeat settings
  pingTimeout: 10000,
  pingInterval: 25000,
});
```

If authentication fails, the connection will be rejected.

## Room Structure

### Room Types

- `user:{userId}` - Personal user room
- `worker:{workerId}` - Worker-specific room
- `partner:{partnerId}` - Partner-specific room
- `project:{projectId}` - Project-specific room
- `manager:{managerId}` - Manager-specific room

### Automatic Room Assignment

Users are automatically joined to relevant rooms based on their role:

#### Workers

- `user:{userId}`
- `worker:{workerId}`
- `project:{projectId}` (if assigned to a project and employment status is active/notice period)

#### Partners

- `user:{userId}`
- `partner:{partnerId}`
- `project:{projectId}` (for all partner's projects)

#### Managers

- `user:{userId}`
- `manager:{managerId}`
- `project:{projectId}` (for projects managed by this manager)

## Events Overview

The system supports **23 real-time events** across 6 categories:

| Category | Events | Description |
|----------|--------|-------------|
| **Daily Reports** | 3 events | Report creation, updates, and completion |
| **Workers** | 5 events | Status changes, employment, approval, presence |
| **Projects** | 3 events | Project lifecycle management |
| **Users** | 2 events | User profile updates and deletion |
| **Partners** | 1 event | Partner profile updates |
| **Managers** | 5 events | Manager assignments, updates, employment |
| **Validation** | 1 event | Presence validation requests |
| **Registration** | 1 event | New registration requests |
| **Project Management** | 2 events | Manager-project assignments |

**Total: 23 Events** - All fully implemented with proper room targeting and complete payload data.

## Daily Report Events

### `daily_report.created`

Emitted when a new daily report is created by a worker.

**Triggered by:** `POST /daily-reports` (Create daily report)

**Target Rooms:**
- `worker:{workerId}` - The worker who created the report
- `project:{projectId}` - The project the report belongs to
- `partner:{partnerId}` - The partner who owns the project
- `manager:{managerId}` - Managers associated with the worker

```typescript
{
  type: 'daily_report.created',
  payload: {
    reportId: string,
    workerId: string,
    projectId: string
  }
}
```

### `daily_report.updated`

Emitted when a daily report status is changed (approved/declined by supervisor).

**Triggered by:** `PATCH /daily-reports/{id}/status` (Change report status)

**Target Rooms:**
- `worker:{workerId}` - The worker who owns the report
- `project:{projectId}` - The project the report belongs to
- `partner:{partnerId}` - The partner who owns the project
- `manager:{managerId}` - Managers associated with the worker

```typescript
{
  type: 'daily_report.updated',
  payload: {
    reportId: string,
    workerId: string,
    projectId: string,
    status: 'approved' | 'declined' | 'submitted' | 'pending'
  }
}
```

### `daily_report.finished`

Emitted when a daily report is finished (work session ended).

**Triggered by:** `POST /daily-reports/{id}/finish` (Finish daily report)

**Target Rooms:**
- `worker:{workerId}` - The worker who finished the report
- `project:{projectId}` - The project the report belongs to
- `partner:{partnerId}` - The partner who owns the project
- `manager:{managerId}` - Managers associated with the worker

```typescript
{
  type: 'daily_report.finished',
  payload: {
    reportId: string,
    workerId: string
  }
}
```

This event is emitted when a daily report is finished, either by the worker themselves or by their partner. Clients should use this event to refresh their timer/report status.

## Worker Events

### `worker.updated`

Emitted when worker information is updated or project assignments change.

**Triggered by:**
- `PATCH /workers/{id}` (Update worker)
- `PATCH /workers/{id}/project` (Assign/unassign project)
- User profile updates that affect workers

**Target Rooms:**
- `worker:{workerId}` - The worker being updated
- `user:{userId}` - The worker's user account
- `project:{projectId}` - Current and/or new project rooms
- `partner:{partnerId}` - The partner who owns the worker

```typescript
{
  type: 'worker.updated',
  payload: {
    workerId: string,
    projectId?: string | null,
    action?: 'project_assigned' | 'project_unassigned' | 'user_info_changed' | string,
    changes?: {
      email?: boolean,
      phoneNumber?: boolean,
      isEmailVerified?: boolean,
      isPhoneVerified?: boolean,
      personalInfo?: boolean
    }
  }
}
```

**Actions:**
- `project_assigned`: Worker was assigned to a project
- `project_unassigned`: Worker was removed from a project
- `user_info_changed`: Worker's user information was updated

### `worker.employment_changed`

Emitted when worker employment status changes (quit, terminated, etc.).

**Triggered by:**
- `POST /workers/{id}/quit` (Worker quits)
- `POST /workers/{id}/terminate` (Worker terminated)

**Target Rooms:**
- `worker:{workerId}` - The worker whose employment changed
- `project:{projectId}` - The project the worker was assigned to
- `partner:{partnerId}` - The partner who owns the worker

```typescript
{
  type: 'worker.employment_changed',
  payload: {
    workerId: string,
    status: 'active' | 'inactive' | 'quit' | 'terminated' | 'quit_notice' | 'terminated_notice',
    effectiveEndDate?: Date
  }
}
```

### `worker.approval_changed`

Emitted when worker approval status changes (approved, rejected, pending).

**Triggered by:** `PATCH /workers/{id}/approval` (Change worker approval status)

**Target Rooms:**
- `worker:{workerId}` - The worker whose approval status changed
- `project:{projectId}` - The project the worker is assigned to
- `partner:{partnerId}` - The partner who owns the worker

```typescript
{
  type: 'worker.approval_changed',
  payload: {
    workerId: string,
    status: 'approved' | 'rejected' | 'pending'
  }
}
```

### `worker.status_changed`

Emitted when worker working status changes (started, finished, paused, passive).

**Triggered by:** `PATCH /workers/{id}/status` (Change working status)

**Target Rooms:**
- `worker:{workerId}` - The worker whose status changed
- `project:{projectId}` - The project the worker is assigned to
- `partner:{partnerId}` - The partner who owns the worker

```typescript
{
  type: 'worker.status_changed',
  payload: {
    workerId: string,
    status: 'started' | 'finished' | 'paused' | 'passive'
  }
}
```

### `worker.presence_changed`

Emitted when worker presence validation status changes.

**Triggered by:** Presence validation workflows (validation confirmed/declined)

**Target Rooms:**
- `worker:{workerId}` - The worker whose presence status changed
- `project:{projectId}` - The project the worker is assigned to
- `partner:{partnerId}` - The partner who owns the worker

```typescript
{
  type: 'worker.presence_changed',
  payload: {
    workerId: string,
    status: 'validated' | 'empty' | 'late'
  }
}
```

## Project Events

### `project.created`

Emitted when a new project is created by a partner.

**Triggered by:** `POST /projects` (Create project)

**Target Rooms:**
- `project:{projectId}` - The newly created project room
- `partner:{partnerId}` - The partner who created the project
- All sockets in the partner room are automatically joined to the project room

```typescript
{
  type: 'project.created',
  payload: {
    projectId: string,
    partnerId: string
  }
}
```

### `project.updated`

Emitted when project information is updated.

**Triggered by:** `PATCH /projects/{id}` (Update project)

**Target Rooms:**
- `project:{projectId}` - The project being updated
- `partner:{partnerId}` - The partner who owns the project

```typescript
{
  type: 'project.updated',
  payload: {
    projectId: string,
    partnerId: string
  }
}
```

### `project.deleted`

Emitted when a project is deleted.

**Triggered by:** `DELETE /projects/{id}` (Delete project)

**Target Rooms:**
- `project:{projectId}` - The project being deleted (before room cleanup)
- `partner:{partnerId}` - The partner who owns the project
- All sockets are removed from the project room after notification

```typescript
{
  type: 'project.deleted',
  payload: {
    projectId: string,
    partnerId: string
  }
}
```

## User Events

### `user.updated`

Emitted when user profile information is updated.

**Triggered by:**
- `PATCH /users/{id}` (Update user profile)
- Email/phone verification workflows
- Profile information changes

**Target Rooms:**
- `user:{userId}` - The user's personal room
- `{roleType}:{entityId}` - Role-specific room (worker, partner, manager)
- If user is a worker, also targets:
  - `worker:{workerId}`
  - `project:{projectId}` (if assigned)
  - `partner:{partnerId}` (if associated)

```typescript
{
  type: 'user.updated',
  payload: {
    userId: string,
    action?: 'email_changed' | 'phone_changed' | 'personal_info_changed' | 'verification_changed' | string,
    changes?: {
      email?: boolean,
      phoneNumber?: boolean,
      isEmailVerified?: boolean,
      isPhoneVerified?: boolean,
      personalInfo?: boolean
    }
  }
}
```

### `user.deleted`

Emitted when a user account is deleted/anonymized.

**Triggered by:** `DELETE /users/{id}` (Delete/anonymize user)

**Target Rooms:**
- `user:{userId}` - The user's personal room (before cleanup)
- Role-specific rooms based on user type

```typescript
{
  type: 'user.deleted',
  payload: {
    userId: string
  }
}
```

## Partner Events

### `partner.updated`

Emitted when partner profile information is updated.

**Triggered by:** `PATCH /partners/{id}` (Update partner profile)

**Target Rooms:**
- `partner:{partnerId}` - The partner being updated
- `manager:{managerId}` - All managers under this partner

```typescript
{
  type: 'partner.updated',
  payload: {
    partnerId: string
  }
}
```

## Manager Events

### `manager.updated`

Emitted when manager information is updated (permissions, approval status).

**Triggered by:**
- `PATCH /managers/{id}/permissions` (Update manager permissions)
- `PATCH /managers/{id}/approval` (Change manager approval status)

**Target Rooms:**
- `manager:{managerId}` - The manager being updated

```typescript
{
  type: 'manager.updated',
  payload: {
    managerId: string,
    changes?: {
      approvalState?: boolean,
      permissionType?: boolean,
      userInfo?: boolean
    }
  }
}
```

### `manager.assigned_to_project`

Emitted when a manager is assigned to a project.

**Triggered by:** `POST /projects/{id}/managers` (Assign manager to project)

**Target Rooms:**
- `manager:{managerId}` - The manager being assigned
- `project:{projectId}` - The project receiving the manager

```typescript
{
  type: 'manager.assigned_to_project',
  payload: {
    managerId: string,
    projectId: string
  }
}
```

### `manager.removed_from_project`

Emitted when a manager is removed from a project.

**Triggered by:** `DELETE /projects/{id}/managers/{managerId}` (Remove manager from project)

**Target Rooms:**
- `manager:{managerId}` - The manager being removed
- `project:{projectId}` - The project losing the manager
- Manager sockets are removed from the project room after notification

```typescript
{
  type: 'manager.removed_from_project',
  payload: {
    managerId: string,
    projectId: string
  }
}
```

### `manager.employment_changed`

Emitted when manager employment status changes.

**Triggered by:**
- `POST /managers/{id}/quit` (Manager quits)
- `POST /managers/{id}/terminate` (Manager terminated)

**Target Rooms:**
- `manager:{managerId}` - The manager whose employment changed
- Associated project rooms

```typescript
{
  type: 'manager.employment_changed',
  payload: {
    managerId: string,
    status: 'active' | 'inactive' | 'quit' | 'terminated' | 'quit_notice' | 'terminated_notice',
    effectiveEndDate?: Date
  }
}
```

### `project_manager.updated`

Emitted when manager-project assignments change (alternative to individual assign/remove events).

**Triggered by:** Manager assignment workflows

**Target Rooms:**
- `project:{projectId}` - The project involved
- `manager:{managerId}` - The manager involved
- Automatic socket room management based on action

```typescript
{
  type: 'project_manager.updated',
  payload: {
    projectId: string,
    managerId: string,
    action: 'assigned' | 'removed'
  }
}
```

## Validation Events

### `presence_validation.requested`

Emitted when a presence validation is requested for a worker.

**Triggered by:**
- `POST /presence-validations` (Request presence validation)
- Automated validation workflows

**Target Rooms:**
- `worker:{workerId}` - The worker who needs to validate presence (primary target)
- `project:{projectId}` - The project the worker is working on
- `partner:{partnerId}` - The partner who owns the project
- `manager:{managerId}` - Managers who need to see validation status

```typescript
{
  type: 'presence_validation.requested',
  payload: {
    validationId: string,
    workerId: string,
    projectId: string
  }
}
```

## Registration Events

### `registration_request.received`

Emitted when a new registration request is submitted.

**Triggered by:** `POST /registration-requests` (Submit registration request)

**Target Rooms:**
- `partner:{partnerId}` - The partner who can approve the request (primary target)
- `manager:{managerId}` - All managers under the partner who can handle requests

```typescript
{
  type: 'registration_request.received',
  payload: {
    requestId: string,
    partnerId: string
  }
}
```

## Error Handling

The server may emit error events in the following cases:

- **Authentication failure** - Invalid or expired JWT token
- **Invalid token** - Malformed or corrupted token
- **Connection timeout** - Client failed to respond to ping within timeout
- **Server errors** - Internal server issues

It's recommended to implement proper error handling and reconnection logic in your client application.

## Example Usage

### Basic Connection

```javascript
import { io } from 'socket.io-client';

const socket = io('/updates', {
  auth: {
    token: 'your-jwt-token',
  },
  // Optional: Override default heartbeat settings
  pingTimeout: 10000,
  pingInterval: 25000,
});

// Handle connection
socket.on('connect', () => {
  console.log('Connected to updates service');
});

// Handle connection errors
socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});

// Handle disconnection
socket.on('disconnect', (reason) => {
  console.log('Disconnected:', reason);
});
```

### Event Listeners

```javascript
// Daily Report Events
socket.on('daily_report.created', (payload) => {
  console.log('New daily report created:', payload);
  // Update UI to show new report
});

socket.on('daily_report.updated', (payload) => {
  console.log('Daily report status changed:', payload);
  // Update report status in UI
  if (payload.status === 'approved') {
    showNotification('Report approved!');
  }
});

// Worker Events
socket.on('worker.status_changed', (payload) => {
  console.log('Worker status changed:', payload);
  // Update worker status indicator
});

socket.on('presence_validation.requested', (payload) => {
  console.log('Presence validation requested:', payload);
  // Show validation prompt to worker
  showPresenceValidationModal(payload.validationId);
});

// Project Events
socket.on('project.created', (payload) => {
  console.log('New project created:', payload);
  // Refresh project list
});

// Registration Events
socket.on('registration_request.received', (payload) => {
  console.log('New registration request:', payload);
  // Show notification to partner/manager
  showRegistrationNotification(payload.requestId);
});
```

### TypeScript Usage

```typescript
interface DailyReportCreatedPayload {
  reportId: string;
  workerId: string;
  projectId: string;
}

interface WorkerStatusChangedPayload {
  workerId: string;
  status: 'started' | 'finished' | 'paused' | 'passive';
}

// Type-safe event listeners
socket.on('daily_report.created', (payload: DailyReportCreatedPayload) => {
  // TypeScript will provide autocomplete and type checking
  updateReportsList(payload.reportId);
});

socket.on('worker.status_changed', (payload: WorkerStatusChangedPayload) => {
  updateWorkerStatus(payload.workerId, payload.status);
});
```

## Best Practices

### 1. Connection Management

```javascript
// Implement automatic reconnection
socket.on('disconnect', (reason) => {
  if (reason === 'io server disconnect') {
    // Server disconnected the client, manual reconnection needed
    socket.connect();
  }
  // Otherwise, socket.io will automatically try to reconnect
});

// Handle token expiration
socket.on('connect_error', (error) => {
  if (error.message === 'Authentication error') {
    // Refresh token and reconnect
    refreshAuthToken().then(newToken => {
      socket.auth.token = newToken;
      socket.connect();
    });
  }
});
```

### 2. Event Handling

```javascript
// Validate payload data
socket.on('daily_report.updated', (payload) => {
  if (!payload.reportId || !payload.status) {
    console.error('Invalid payload received:', payload);
    return;
  }

  // Process valid payload
  handleReportUpdate(payload);
});

// Implement error boundaries
function handleReportUpdate(payload) {
  try {
    updateReportInUI(payload);
  } catch (error) {
    console.error('Error handling report update:', error);
    // Fallback behavior
  }
}
```

### 3. Room Awareness

```javascript
// Be aware of your role and expected events
const userRole = getCurrentUserRole(); // 'worker', 'partner', 'manager'

if (userRole === 'worker') {
  // Workers should listen for their specific events
  socket.on('presence_validation.requested', handlePresenceValidation);
  socket.on('daily_report.updated', handleReportStatusChange);
} else if (userRole === 'partner') {
  // Partners should listen for project and registration events
  socket.on('registration_request.received', handleNewRegistration);
  socket.on('project.updated', handleProjectChange);
}
```

### 4. Performance Optimization

```javascript
// Clean up listeners when component unmounts
useEffect(() => {
  socket.on('worker.updated', handleWorkerUpdate);

  return () => {
    socket.off('worker.updated', handleWorkerUpdate);
  };
}, []);

// Debounce rapid updates
const debouncedUpdateHandler = debounce((payload) => {
  updateUI(payload);
}, 100);

socket.on('worker.status_changed', debouncedUpdateHandler);
```

### 5. Security

```javascript
// Always validate data from server
function validatePayload(payload, expectedFields) {
  return expectedFields.every(field =>
    payload.hasOwnProperty(field) && payload[field] != null
  );
}

// Use HTTPS/WSS in production
const socket = io('/updates', {
  auth: { token: getSecureToken() },
  secure: process.env.NODE_ENV === 'production',
  transports: ['websocket'], // Prefer websocket over polling
});
```

## Notes

- **Room Management**: Users are automatically joined to relevant rooms based on their role and assignments
- **Event Targeting**: All events use intelligent room targeting to ensure only relevant users receive updates
- **Payload Completeness**: All event payloads include sufficient context data for proper handling
- **Real-time Guarantee**: Events are emitted immediately when the corresponding actions occur
- **Type Safety**: Use TypeScript interfaces for better development experience and error prevention

> **Important:** Only one manager per project is supported. Assigning a new manager to a project will automatically remove the previous one and emit appropriate events.
